{"name": "repository-designer", "version": "0.1.0", "private": true, "dependencies": {"@dqbd/tiktoken": "^1.0.21", "@elastic/elasticsearch": "^9.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-scripts": "5.0.1", "styled-components": "^6.1.1"}, "devDependencies": {"@craco/craco": "^7.1.0", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "concurrently": "^7.6.0", "cors": "^2.8.5", "crypto-browserify": "^3.12.1", "events": "^3.3.0", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "querystring-es3": "^0.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "string-replace-loader": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "es-proxy": "node src/components/elasticsearch-proxy.js", "dev": "concurrently \"npm run es-proxy\" \"npm start\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}