const webpack = require('webpack');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // Add fallbacks for Node.js modules
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        "buffer": require.resolve("buffer"),
        "crypto": require.resolve("crypto-browserify"),
        "stream": require.resolve("stream-browserify"),
        "https": require.resolve("https-browserify"),
        "os": require.resolve("os-browserify/browser"),
        "url": require.resolve("url"),
        "path": require.resolve("path-browserify"),
        "fs": false,
        "net": false,
        "tls": false,
        "child_process": false,
        "zlib": require.resolve("browserify-zlib"),
        "http": require.resolve("stream-http"),
        "assert": require.resolve("assert"),
        "util": require.resolve("util"),
        // 添加更多Node.js模块的fallback
        "events": require.resolve("events"),
        "querystring": require.resolve("querystring-es3"),
        "timers": false,
        "console": false,
        "process": require.resolve("process/browser"),
        "vm": false,
        "worker_threads": false,
        "async_hooks": false,
        "diagnostics_channel": false,
        "dns": false,
        "http2": false,
        "perf_hooks": false,
        "sqlite": false,
        "util/types": false
      };

      // 添加别名来处理node:前缀的导入
      webpackConfig.resolve.alias = {
        ...webpackConfig.resolve.alias,
        "node:assert": require.resolve("assert"),
        "node:buffer": require.resolve("buffer"),
        "node:crypto": require.resolve("crypto-browserify"),
        "node:events": require.resolve("events"),
        "node:http": require.resolve("stream-http"),
        "node:https": require.resolve("https-browserify"),
        "node:os": require.resolve("os-browserify/browser"),
        "node:path": require.resolve("path-browserify"),
        "node:process": require.resolve("process/browser"),
        "node:querystring": require.resolve("querystring-es3"),
        "node:stream": require.resolve("stream-browserify"),
        "node:url": require.resolve("url"),
        "node:util": require.resolve("util"),
        "node:zlib": require.resolve("browserify-zlib"),
        // 对于不支持的模块，指向空对象
        "node:async_hooks": false,
        "node:console": false,
        "node:diagnostics_channel": false,
        "node:dns": false,
        "node:http2": false,
        "node:net": false,
        "node:perf_hooks": false,
        "node:sqlite": false,
        "node:timers/promises": false,
        "node:tls": false,
        "node:util/types": false,
        "node:worker_threads": false
      };

      // Add plugins
      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new webpack.ProvidePlugin({
          Buffer: ['buffer', 'Buffer'],
          process: 'process/browser',
        }),
        // 使用NormalModuleReplacementPlugin来替换node:前缀的模块
        new webpack.NormalModuleReplacementPlugin(
          /^node:events$/,
          require.resolve('events')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:assert$/,
          require.resolve('assert')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:buffer$/,
          require.resolve('buffer')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:crypto$/,
          require.resolve('crypto-browserify')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:http$/,
          require.resolve('stream-http')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:https$/,
          require.resolve('https-browserify')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:os$/,
          require.resolve('os-browserify/browser')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:path$/,
          require.resolve('path-browserify')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:process$/,
          require.resolve('process/browser')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:querystring$/,
          require.resolve('querystring-es3')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:stream$/,
          require.resolve('stream-browserify')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:url$/,
          require.resolve('url')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:util$/,
          require.resolve('util')
        ),
        new webpack.NormalModuleReplacementPlugin(
          /^node:zlib$/,
          require.resolve('browserify-zlib')
        ),
        // 专门处理@elastic/elasticsearch包中的node:events导入
        new webpack.NormalModuleReplacementPlugin(
          /node_modules\/@elastic\/.*\/.*\.js$/,
          (resource) => {
            // 如果资源请求包含node:events，替换为events
            if (resource.request === 'node:events') {
              resource.request = 'events';
            }
          }
        ),
        // 忽略不支持的模块
        new webpack.IgnorePlugin({
          resourceRegExp: /^node:(async_hooks|console|diagnostics_channel|dns|http2|net|perf_hooks|sqlite|timers\/promises|tls|util\/types|worker_threads)$/,
        }),
      ];

      // 添加模块规则来处理node:前缀
      webpackConfig.module.rules.push({
        test: /\.m?js$/,
        resolve: {
          fullySpecified: false,
        },
      });

      // 添加特殊的loader来处理@elastic包中的node:前缀导入
      webpackConfig.module.rules.push({
        test: /node_modules\/@elastic\/.*\.js$/,
        use: {
          loader: 'string-replace-loader',
          options: {
            search: /require\(['"]node:([^'"]+)['"]\)/g,
            replace: "require('$1')",
            flags: 'g'
          }
        }
      });

      return webpackConfig;
    },
  },
  // 添加开发服务器配置
  devServer: {
    setupMiddlewares: (middlewares, devServer) => {
      // 处理一些开发服务器的警告
      return middlewares;
    },
  },
};
