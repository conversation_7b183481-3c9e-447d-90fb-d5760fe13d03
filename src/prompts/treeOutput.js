/**
 * 树输出增强相关的prompt模板
 */

/**
 * 生成原始提示词摘要的模板
 * @param {string} originalPrompt - 原始提示词内容
 * @returns {string} 格式化后的prompt
 */
export const createOriginalPromptSummaryPrompt = (originalPrompt) => {
  return `# Role：需求分析师
  你是一个需求分析师，擅长在大量信息中鉴别出关键点，为管理者提供核心信息以支持决策。

  ## Goals
  用户将以任何风格给出需求描述文本，你需要深入分析并生成具有逻辑性、完整性、关键信息正确的摘要。

  ## Constraints
  - 按照MECE原则提取关键需求点和功能点，确保需求描述文本中的各个要点都存在于摘要中。
  - 需要严格遵循用户的描述，不能篡改或编造用户需求。
  - 对于关键信息或关键词需要进行反思：在当前上下文中是否有歧义或违背用户意愿？

  ## OutputFormat
  返回必须严格遵循以下JSON结构：
  \`\`\`json
  {
    "key subtask": [{
      "index": 0,
      "description": "数据层代码开发",
      "key points": [
        {
          "index": 0,
          "content": "表名为ad_contract_unit"
        },
        {
          "index": 1,
          "content": "基于主键的查询功能"
        }
      ]
    },{
      "index": 1,
      "description": "工具类代码开发",
      "key points": [
        {
          "index": 0,
          "content": "支持多维度查询"
        },
        {
          "index": 1,
          "content": "查询维度取值的笛卡尔积不能超过10000"
        }
      ]
    }
  ]}
  \`\`\`

  ## Input
  ${originalPrompt}`
};

/**
 * 生成增强提示词摘要的模板
 * @param {string} enhancedPrompt - 增强后的提示词内容
 * @returns {string} 格式化后的prompt
 */
export const createEnhancedPromptSummaryPrompt = (enhancedPrompt) => {
  return `# 任务说明
你是一位专业的文档分析师，需要对经过AI增强优化的提示词进行深度分析和摘要提取。

# 增强后提示词内容
${enhancedPrompt}

# 分析要求
请仔细分析上述增强后的提示词，提取并总结以下关键信息：

## 1. 优化后的核心目标
- 增强后的主要目标是什么？
- 相比原始版本有哪些改进？

## 2. 完善的需求体系
- 新增或完善的功能需求
- 技术栈或工具的具体化
- 性能或质量标准的细化

## 3. 规范化的约束条件
- 明确的限制条件和边界
- 标准化的规范要求
- 质量控制标准

## 4. 标准化的输出格式
- 结构化的输出要求
- 格式规范和模板
- 文档或代码标准

## 5. 增强特色
- AI优化后的独特优势
- 专业化的指导原则
- 可操作性的提升

# 输出要求
请以简洁明了的方式总结上述分析结果，重点突出AI增强后的价值和改进点。总结应该：
- 长度控制在250-350字
- 使用结构化的表述
- 突出最重要的4-6个关键改进点
- 体现专业性和实用性

请直接输出摘要内容，不需要额外的格式标记。`;
};

/**
 * 生成项目开发AI提示词的模板
 * @param {Object} treeData - 项目树结构数据
 * @param {string} asciiTree - ASCII格式的树结构字符串
 * @param {Array} capabilities - 功能描述列表
 * @param {Object} projectInfo - 项目信息对象
 * @returns {string} 格式化后的prompt
 */
export const createProjectDevelopmentPrompt = (treeData, asciiTree, capabilities, projectInfo) => {
  const {
    projectName,
    projectType,
    projectCategory,
    techRole,
    frameworks,
    libraries,
    techRequirements,
    dataFlowPattern,
    moduleRelationships,
    dependencyManagement,
    runningInstructions,
    techStackText
  } = projectInfo;

  // 构建功能描述文本
  const capabilitiesText = capabilities.length > 0 
    ? capabilities.map(cap => `- ${cap}`).join('\n')
    : '- 为每个文件和目录添加适当的功能和实现';

  // 构建结构化Markdown格式的提示词
  return `# 项目开发需求

## 角色

我希望你作为一名${techRole}，帮我实现以下项目：

## 项目需求

我需要构建一个名为 "${projectName}" 的${projectCategory}项目。我已经设计好了详细的项目结构，请务必**严格按照这个预定义的结构**实现所有代码。每个模块的功能必须与其描述保持一致。${dependencyManagement}${runningInstructions}

## 项目结构

\`\`\`
${asciiTree}
\`\`\`

## 功能描述

以下是每个组件及其功能的详细描述，请确保你实现的代码满足这些功能要求：

${capabilitiesText}

## 模块交互与数据流

请特别注意以下关键点：

1. **模块依赖关系**：确保各模块之间的依赖关系清晰，避免循环依赖
2. **数据流向**：数据应该按照合理的流向传递，例如：
   ${dataFlowPattern}
3. **接口设计**：模块间的接口应保持一致性和清晰性
4. **错误处理**：确保在关键节点有适当的错误处理机制${moduleRelationships}

## 技术栈限定

${techStackText}

## 严格遵循结构

请注意：
- **不要新增**额外的文件或目录
- **不要修改**预定义的项目结构
- 每个文件的功能必须与其在项目结构中的描述一致
- 如果某个模块缺少必要的依赖项，可以在现有结构内部添加相应的导入语句

## 预期输出

请为每个文件生成完整的代码实现，确保：
1. 代码严格遵循预定义的项目结构
2. 实现的功能与每个组件的描述一致
3. 模块之间有清晰的交互关系
4. 代码可以直接运行
5. 有适当的注释和文档
6. 遵循${projectType}的最佳实践

请以文件路径为标题，依次输出每个文件的代码实现。`;
};

/**
 * Prompt增强的模板（流式处理）- 具备反思能力的Agent
 * @param {string} promptText - 原始prompt文本
 * @returns {string} 格式化后的prompt
 */
export const createPromptEnhancementPrompt = (promptText) => {
  return `# 角色定义
你是一位资深的Prompt工程专家和软件架构师，具备深度批判性思维和系统优化能力。你的核心职责是识别原始提示词中的各种问题和缺陷，并进行深度重构优化。

# 任务目标
对下面的项目开发提示词进行**批判性分析**和**系统性重构**，通过定向反思和非定向反思，输出一个显著改进的高质量prompt。

# 原始提示词
${promptText}

# 🎯 定向反思框架

## 1. 项目结构合理性审查
**优化原则**: 不删除现有目录或文件，但允许修改和新增以完善功能

**核心检查点**:
- ✅ **命名规范**: 项目名、目录名、文件名是否为标准英文
- ✅ **结构逻辑**: 目录层次是否合理，模块划分是否清晰
- ✅ **最佳实践**: 是否遵循对应技术栈的行业标准
- ✅ **扩展性**: 结构是否支持未来功能扩展
- ✅ **维护性**: 是否便于代码维护和团队协作

**优化策略**:
- 补充缺失的关键文件（如 requirements.txt, README.md, main.py等）
- 新增必要的目录以支持完整的开发工作流
- 优化目录命名，确保符合技术栈约定
- 添加配置和文档相关的结构

## 2. 增量研发生态建设
**优化原则**: 构建支持增量开发和团队协作的完整生态体系

**关键要素**:
- 📋 **design.md文件**: 每个目录必须包含设计文档
  - 目录概要：模块的总体设计思路和定位
  - 文件说明：每个文件的职责和接口定义
  - 设计模式：采用的设计模式和架构决策
  - 开发规范：代码风格、命名约定、注释标准
  - 扩展指南：新功能开发路径和注意事项
- 📋 **规范体系**: 代码规范、提交规范、测试规范
- 📋 **协作机制**: 新成员onboarding、知识传承、代码review流程
- 📋 **版本管理**: 分支策略、发布流程、变更管理

**反思要点**:
- 是否存在与增量研发需求相冲突的不合理限制
- 项目结构是否便于后续功能迭代和扩展
- 新成员是否能快速理解项目架构和开发流程
- 是否建立了清晰的文档体系和知识管理机制

## 3. 功能描述精准化
**优化原则**: 确保每个目录和文件的描述清晰、准确、可执行

**反思维度**:
- ❓ **描述精确性**: 功能描述是否具体明确？是否有歧义？
- ❓ **技术深度**: 描述是否体现专业的技术深度？
- ❓ **实现指导**: 描述是否能指导具体的代码实现？
- ❓ **关联性**: 各模块描述是否体现了相互关系？

**优化策略**:
- 将模糊描述改写为具体的功能规格
- 补充技术细节和实现要点
- 明确各模块的输入输出和接口
- 强化模块间的协作关系描述

## 4. 角色专业性提升
**优化原则**: 打造更专业、更具体、更有权威性的技术角色

**反思维度**:
- ❓ **专业程度**: 角色定义是否足够专业和权威？
- ❓ **技术深度**: 是否体现了高级工程师的技术素养？
- ❓ **职责边界**: 角色的能力范围和职责是否清晰？
- ❓ **行业视野**: 是否体现了对行业趋势和最佳实践的了解？

**优化策略**:
- 升级角色定位，强调资深专家身份
- 补充技术领域的专业能力描述
- 明确质量标准和交付期望
- 融入行业最佳实践的要求

## 5. 隐性需求挖掘
**优化原则**: 挖掘需求背后的隐性要求，并将其具象化到项目结构中

**反思维度**:
- ❓ **隐性需求**: 项目需求中隐含了哪些未明确表达的能力？
- ❓ **质量要求**: 是否需要测试、文档、部署等支撑能力？
- ❓ **运维需求**: 是否需要日志、监控、配置管理等？
- ❓ **协作需求**: 是否需要支持团队协作的结构和规范？

**具象化策略**:
- 补充质量保证相关的文件和目录
- 添加开发工具配置（如 .gitignore, pre-commit 等）
- 完善文档体系（README, API文档, 架构文档等）
- 强化测试和部署相关的结构

# 🔄 非定向反思探索

## 整体优化维度
完成定向反思后，从以下角度进行全面审视：

### 1. Prompt架构优化
- **信息组织**: 是否有更清晰的信息组织方式？是否有重复冗余的信息？例如同样的信息被多次强调
- **逻辑流程**: 各部分之间的逻辑关系是否合理？
- **重点突出**: 关键信息是否得到充分强调？

### 2. 技术权威性提升
- **专业术语**: 是否使用了准确的技术术语？
- **最佳实践**: 是否融入了最新的行业最佳实践？
- **标准规范**: 是否符合相关技术标准？

### 3. 执行指导强化
- **可操作性**: 每个指令是否具体可执行？
- **验收标准**: 是否有明确的质量验收标准？
- **交付规范**: 是否明确了代码交付的格式和要求？

### 4. 前瞻性设计
- **可维护性**: 是否考虑了长期维护的需求？
- **可扩展性**: 是否为未来发展预留了空间？
- **团队协作**: 是否支持多人协作开发？

# 🎯 输出要求

请按照以下格式输出优化结果：
## 优化后的Prompt
[完整的优化后提示词内容]

# 注意
- 只输出优化后的prompt，不要有其他内容
- 确保prompt的格式正确，可以被解析
- 分析要深入具体，避免泛泛而谈
- 改进建议要有可操作性和实用价值
`;
};

/**
 * Prompt增强的JSON格式模板 - 具备反思能力的Agent
 * @param {string} promptText - 原始prompt文本
 * @returns {string} 格式化后的prompt
 */
export const createPromptEnhancementJsonPrompt = (promptText) => {
  return `
# 角色定义
你是一位具备深度反思能力的高级Prompt工程师和项目架构专家。你擅长系统性分析、结构化思考和专业优化。

# 任务说明
请对下面的项目开发提示词进行深度反思分析和专业优化，并以结构化的JSON格式返回分析结果。

# 原始提示词
${promptText}

# 反思分析体系

## 🎯 定向反思分析

### 1. 项目结构合理性审查
**核心检查点**：
- ✅ 命名规范：项目名、目录名、文件名是否为标准英文
- ✅ 结构逻辑：目录层次是否合理，模块划分是否清晰
- ✅ 最佳实践：是否遵循对应技术栈的行业标准
- ✅ 扩展性：结构是否支持未来功能扩展
- ✅ 维护性：是否便于代码维护和团队协作

### 2. 增量研发生态建设
**关键要素**：
- 📋 design.md文件：每个目录必须包含设计文档
- 📋 文档内容：目录概要、文件说明、设计模式、开发规范
- 📋 规范体系：代码规范、提交规范、测试规范
- 📋 扩展指南：新功能开发指导、架构演进路径
- 📋 团队协作：新成员onboarding、知识传承

## 🔍 非定向反思探索

### 1. 角色专业性提升
- 角色定位是否足够专业和权威
- 技术领域专长是否明确
- 职责边界和能力范围是否清晰

### 2. 指令执行力优化
- 指令的具体性和可操作性
- 优先级和重要性的明确程度
- 验收标准的可衡量性

### 3. 技术深度强化
- 技术要求的专业程度
- 非功能性需求的覆盖度
- 行业最佳实践的融入程度

### 4. 质量保障机制
- 输出质量标准的明确性
- 质量检查点的设置
- 持续改进机制的建立

# 优化策略框架

## 分析阶段
1. **问题识别**：系统性发现原prompt的不足
2. **机会挖掘**：识别可以提升的优化点
3. **影响评估**：评估改进的价值和必要性

## 设计阶段
1. **结构重构**：重新设计prompt的组织架构
2. **内容增强**：补充关键信息和技术细节
3. **标准制定**：建立明确的质量和执行标准

## 实施阶段
1. **专业化改写**：提升语言的专业性和准确性
2. **系统化整合**：确保各部分逻辑一致性
3. **前瞻性设计**：考虑长期发展和维护需求

# 返回格式
返回必须严格遵循以下JSON结构：
\`\`\`json
{
  "optimizedPrompt": "完整的优化后提示词内容（不包含额外标记）",
  "reflectionAnalysis": {
    "structuralReflection": {
      "namingIssues": ["发现的命名问题"],
      "structuralOptimizations": ["结构优化建议"],
      "designMdRequirements": ["design.md文件的具体要求"]
    },
    "incrementalDevelopmentReflection": {
      "documentationGaps": ["文档缺失分析"],
      "scalabilityIssues": ["可扩展性问题"],
      "maintenanceOptimizations": ["维护性改进建议"]
    },
    "openEndedReflection": {
      "roleDefinitionImprovements": ["角色定义优化点"],
      "instructionClarityEnhancements": ["指令清晰度提升"],
      "technicalDepthUpgrades": ["技术深度强化"],
      "qualityStandardRefinements": ["质量标准完善"]
    }
  },
  "improvementPoints": [
    {
      "category": "定向反思-项目结构",
      "type": "结构优化",
      "description": "具体的改进说明和预期效果",
      "priority": "高/中/低",
      "implementation": "具体实施方案"
    },
    {
      "category": "定向反思-增量研发",
      "type": "文档体系",
      "description": "design.md文件体系建设",
      "priority": "高",
      "implementation": "在每个目录添加design.md文件要求"
    },
    {
      "category": "非定向反思",
      "type": "角色优化",
      "description": "角色定义的专业性提升",
      "priority": "中",
      "implementation": "重新定义技术角色的专业领域"
    }
  ],
  "qualityMetrics": {
    "professionalismScore": "专业性评分(1-10)",
    "clarityScore": "清晰度评分(1-10)",
    "completenessScore": "完整性评分(1-10)",
    "scalabilityScore": "可扩展性评分(1-10)"
  }
}
\`\`\`

# 核心要求
1. **深度反思**：不仅仅是表面优化，要进行深层次的分析
2. **系统思考**：从项目全生命周期角度考虑优化
3. **专业标准**：体现高级工程师的专业水准
4. **实用导向**：确保优化后的prompt具有更强的实用价值
5. **前瞻设计**：考虑未来发展和维护需求

# 特别强调
- 必须为每个目录强制要求design.md文件
- 所有命名必须符合英文规范
- 强化对增量开发场景的支持
- 提升prompt的技术深度和专业性

# 重要提示
- 只输出JSON格式，不要有其他内容
- 确保JSON格式正确，可以被解析
- 分析要深入具体，避免泛泛而谈
- 改进建议要有可操作性和实用价值`;
};

/**
 * 分析项目信息的工具函数
 * @param {Object} treeData - 项目树结构数据
 * @param {string} asciiTree - ASCII格式的树结构字符串
 * @param {Array} detectedFiles - 检测到的文件列表
 * @param {string} selectedProjectType - 用户选择的项目类型
 * @returns {Object} 项目信息对象
 */
export const analyzeProjectInfo = (treeData, asciiTree, detectedFiles = [], selectedProjectType = null) => {
  // 提取项目名称
  const projectName = treeData?.name || '未命名项目';
  
  // 使用用户选择的项目类型，如果没有提供则尝试推断
  let projectType = selectedProjectType || '未知类型';
  let frameworks = [];
  let libraries = [];
  
  // 如果没有提供选择的项目类型，才进行推断
  if (!selectedProjectType) {
    // 尝试从项目名称或树结构判断项目类型和框架
    if (projectName.toLowerCase().includes('python') || 
        asciiTree.toLowerCase().includes('.py')) {
      projectType = 'Python';
    } else if (projectName.toLowerCase().includes('react') || 
              asciiTree.toLowerCase().includes('.jsx') || 
              asciiTree.toLowerCase().includes('.tsx')) {
      projectType = 'React';
    } else if (projectName.toLowerCase().includes('vue') || 
              asciiTree.toLowerCase().includes('.vue')) {
      projectType = 'Vue.js';
    } else if (asciiTree.toLowerCase().includes('.java')) {
      projectType = 'Java';
    } else if (asciiTree.toLowerCase().includes('.go')) {
      projectType = 'Go';
    }
  }
  
  // 根据项目类型检测相关框架和库
  if (projectType === 'Python' || projectType === 'backend-python' || projectType === 'llm-python' || projectType === 'python-algorithm') {
    // 检测常见的Python框架和库
    if (asciiTree.toLowerCase().includes('django')) {
      frameworks.push('Django');
    }
    if (asciiTree.toLowerCase().includes('flask')) {
      frameworks.push('Flask');
    }
    if (asciiTree.toLowerCase().includes('fastapi')) {
      frameworks.push('FastAPI');
    }
    if (asciiTree.toLowerCase().includes('pandas') || 
        asciiTree.toLowerCase().includes('dataframe')) {
      libraries.push('Pandas');
    }
    if (asciiTree.toLowerCase().includes('numpy')) {
      libraries.push('NumPy');
    }
    if (asciiTree.toLowerCase().includes('tensorflow') || 
        asciiTree.toLowerCase().includes('keras')) {
      libraries.push('TensorFlow');
    }
    if (asciiTree.toLowerCase().includes('torch') || 
        asciiTree.toLowerCase().includes('pytorch')) {
      libraries.push('PyTorch');
    }
  } else if (projectType === 'React' || projectType === 'frontend') {
    // 检测React相关库
    if (asciiTree.toLowerCase().includes('redux')) {
      libraries.push('Redux');
    }
    if (asciiTree.toLowerCase().includes('router')) {
      libraries.push('React Router');
    }
  } else if (projectType === 'Vue.js') {
    // 检测Vue相关库
    if (asciiTree.toLowerCase().includes('vuex') || 
        asciiTree.toLowerCase().includes('pinia')) {
      libraries.push('Vuex/Pinia');
    }
    if (asciiTree.toLowerCase().includes('router')) {
      libraries.push('Vue Router');
    }
  } else if (projectType === 'Java' || projectType === 'backend-java') {
    // 检测Java相关框架
    if (asciiTree.toLowerCase().includes('spring')) {
      frameworks.push('Spring Boot');
    }
  } else if (projectType === 'Go') {
    // 检测Go相关框架
    if (asciiTree.toLowerCase().includes('gin')) {
      frameworks.push('Gin');
    }
  }
  
  // 判断是否为算法或机器学习项目
  const isMLProject = 
    projectType === 'llm-python' ||
    projectType === 'python-algorithm' ||
    projectName.toLowerCase().includes('prediction') || 
    projectName.toLowerCase().includes('ml') || 
    projectName.toLowerCase().includes('ai') || 
    asciiTree.toLowerCase().includes('model') || 
    asciiTree.toLowerCase().includes('train') || 
    asciiTree.toLowerCase().includes('feature');
  
  // 确定项目类型
  let projectCategory = '软件';
  let techRequirements = '需要包含适当的错误处理和日志记录';
  
  if (isMLProject) {
    projectCategory = '机器学习/数据分析';
    techRequirements = '需要包含适当的数据处理、特征工程和模型训练步骤';
  } else if (projectType === 'frontend' || 
            projectType === 'React' ||
            projectType === 'Vue.js' ||
            asciiTree.toLowerCase().includes('component') || 
            projectName.toLowerCase().includes('ui') ||
            projectName.toLowerCase().includes('frontend') ||
            asciiTree.toLowerCase().includes('src/components') ||
            asciiTree.toLowerCase().includes('src/views') ||
            asciiTree.toLowerCase().includes('src/pages') ||
            asciiTree.toLowerCase().includes('public/index.html') ||
            asciiTree.toLowerCase().includes('webpack.config') ||
            asciiTree.toLowerCase().includes('.jsx') ||
            asciiTree.toLowerCase().includes('.vue')) {
    projectCategory = '前端/UI';
    techRequirements = '需要包含响应式设计、组件文档和用户交互处理';
  } else if (projectType === 'backend-java' ||
            projectType === 'backend-python' ||
            projectType === 'backend-nodejs' ||
            asciiTree.toLowerCase().includes('controller') || 
            asciiTree.toLowerCase().includes('service') ||
            asciiTree.toLowerCase().includes('repository') ||
            asciiTree.toLowerCase().includes('entity') ||
            asciiTree.toLowerCase().includes('model') ||
            projectName.toLowerCase().includes('api') ||
            asciiTree.toLowerCase().includes('server') ||
            asciiTree.toLowerCase().includes('app.py') ||
            asciiTree.toLowerCase().includes('main.py') ||
            asciiTree.toLowerCase().includes('wsgi.py') ||
            asciiTree.toLowerCase().includes('application.properties') ||
            asciiTree.toLowerCase().includes('pom.xml') ||
            asciiTree.toLowerCase().includes('build.gradle')) {
    projectCategory = '后端/API';
    techRequirements = '需要包含适当的API文档、错误处理和数据验证';
  }
  
  // 确定技术角色
  let techRole = '高级软件工程师';
  if (isMLProject) {
    techRole = '高级机器学习工程师';
  } else if (projectCategory === '后端/API') {
    techRole = '高级后端工程师';
  } else if (projectCategory === '前端/UI') {
    techRole = '高级前端工程师';
  }
  
  // 构建技术栈部分
  let techStackText = `- 主要编程语言：${projectType}`;
  
  if (frameworks.length > 0) {
    techStackText += `\n- 框架：${frameworks.join(', ')}`;
  }
  
  if (libraries.length > 0) {
    techStackText += `\n- 库/工具：${libraries.join(', ')}`;
  }
  
  techStackText += `\n- 代码风格：简洁、模块化、高可维护性
- ${techRequirements}
- 遵循行业最佳实践和设计模式`;
  
  // 确定数据流模式
  let dataFlowPattern = '';
  
  if (isMLProject) {
    dataFlowPattern = `- 数据加载(raw_data) → 数据预处理(processed_data) → 特征工程(features) → 模型定义 → 模型训练 → 模型评估`;
  } else if (projectCategory === '后端/API') {
    dataFlowPattern = `- 请求接收 → 输入验证 → 业务逻辑处理 → 数据持久化 → 响应生成`;
  } else if (projectCategory === '前端/UI') {
    dataFlowPattern = `- 用户交互 → 状态管理 → API调用 → 数据展示 → 用户反馈`;
  } else {
    dataFlowPattern = `- 数据输入 → 业务逻辑处理 → 数据输出/展示`;
  }

  // 识别关键模块关系
  let moduleRelationships = '';
  let moduleNames = [];
  
  // 从项目结构中提取模块名称
  if (treeData && treeData.children) {
    // 一级目录通常是主要模块
    moduleNames = treeData.children
      .filter(child => child.type === 'folder')
      .map(child => child.name);
  }
  
  if (moduleNames.length > 1) {
    moduleRelationships = `\n\n以下是本项目关键模块之间的交互关系：\n`;
    
    // 根据不同项目类型生成不同的模块关系描述
    if (isMLProject) {
      if (moduleNames.includes('data') && moduleNames.includes('src')) {
        moduleRelationships += `- **data模块**负责数据存储和管理，**src模块**中的代码会访问这些数据进行处理和训练\n`;
      }
      if (moduleNames.includes('src') && moduleNames.includes('experiments')) {
        moduleRelationships += `- **src模块**提供核心算法实现，**experiments模块**使用这些算法进行实验\n`;
      }
      if (moduleNames.includes('model')) {
        moduleRelationships += `- **model模块**存储训练好的模型，由训练脚本生成并由评估脚本加载\n`;
      }
    } else if (projectCategory === '前端/UI' || projectCategory === '后端/API') {
      // 前端/后端通用关系描述
      for (let i = 0; i < moduleNames.length; i++) {
        for (let j = i + 1; j < moduleNames.length; j++) {
          // 只描述有可能存在关联的模块
          if (
            (moduleNames[i] === 'components' && moduleNames[j] === 'pages') ||
            (moduleNames[i] === 'api' && moduleNames[j] === 'services') ||
            (moduleNames[i] === 'utils' && (moduleNames[j] === 'components' || moduleNames[j] === 'services')) ||
            (moduleNames[i] === 'models' && moduleNames[j] === 'services')
          ) {
            moduleRelationships += `- **${moduleNames[i]}模块**和**${moduleNames[j]}模块**之间存在依赖关系\n`;
          }
        }
      }
    }
    
    // 如果没有生成任何具体关系，则提供通用描述
    if (moduleRelationships === `\n\n以下是本项目关键模块之间的交互关系：\n`) {
      moduleRelationships += `- 项目中的各个模块需要保持低耦合高内聚的设计原则\n`;
      moduleRelationships += `- 避免循环依赖，较底层的模块不应依赖较高层的模块\n`;
    }
  }
  
  // 检测配置文件和核心文件
  const hasPackageJson = detectedFiles.some(file => file.endsWith('package.json'));
  const hasRequirementsTxt = detectedFiles.some(file => file.endsWith('requirements.txt'));
  const hasDockerfile = detectedFiles.some(file => file.endsWith('Dockerfile') || file.endsWith('docker-compose.yml'));
  const hasMainPy = detectedFiles.some(file => file.endsWith('main.py'));
  const hasIndexJs = detectedFiles.some(file => file.endsWith('index.js') || file.endsWith('index.jsx') || file.endsWith('index.tsx'));
  const hasMakefile = detectedFiles.some(file => file.endsWith('Makefile'));
  
  // 构建依赖管理建议
  let dependencyManagement = '';
  if (projectType === 'Python') {
    if (!hasRequirementsTxt) {
      dependencyManagement = `\n\n请在实现时确保添加所有必要的依赖项到requirements.txt文件中。`;
    }
  } else if (projectType === 'React' || projectType === 'Vue.js' || projectType === 'JavaScript') {
    if (!hasPackageJson) {
      dependencyManagement = `\n\n请确保package.json文件中包含所有必要的依赖项。`;
    }
  }
  
  // 构建启动运行建议
  let runningInstructions = '';
  if (hasDockerfile) {
    runningInstructions = `\n\n该项目包含Docker配置，请确保实现能够在Docker环境中正确运行。`;
  } else if (hasMainPy && projectType === 'Python') {
    runningInstructions = `\n\n请确保实现中的main.py作为项目的入口点，可以直接运行。`;
  } else if (hasIndexJs && (projectType === 'React' || projectType === 'Vue.js')) {
    runningInstructions = `\n\n请确保index文件正确配置为应用程序的入口点。`;
  } else if (hasMakefile) {
    runningInstructions = `\n\n该项目使用Makefile进行构建，请确保Makefile中包含适当的构建和运行目标。`;
  }

  return {
    projectName,
    projectType,
    projectCategory,
    techRole,
    frameworks,
    libraries,
    techRequirements,
    dataFlowPattern,
    moduleRelationships,
    dependencyManagement,
    runningInstructions,
    techStackText,
    isMLProject
  };
}; 